import { PermissionEnum } from '@/shared/types/permission';

// Giao diện cho ModernMenuItem với trường permission
export interface ModernMenuItem {
  id: string;
  label: string;
  path: string;
  icon: string;
  keywords: string[];
  permission: PermissionEnum;
}

/**
 * <PERSON><PERSON> sách menu cho người dùng thông thường
 */
export const userMenuItems: ModernMenuItem[] = [
  {
    id: 'home',
    label: 'common.home',
    path: '/',
    icon: 'home',
    keywords: ['trang chủ', 'home', 'main', 'dashboard', 'trang chinh'],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN,
  },
  {
    id: 'profile',
    label: 'common.profile',
    path: '/profile',
    icon: 'user',
    keywords: ['profile', 'hồ sơ', 'ho so', 'tài khoản', 'tai khoan', 'account', 'user'],
    permission: PermissionEnum.USER_UPDATE_PROFILE,
  },
  {
    id: 'projects',
    label: 'projects:title',
    path: '/projects',
    icon: 'folder',
    keywords: ['projects', 'dự án', 'du an', 'project', 'tasks', 'công việc', 'cong viec'],
    permission: PermissionEnum.PROJECT_VIEW_LIST,
  },
  {
    id: 'tasks',
    label: 'tasks:title',
    path: '/tasks',
    icon: 'list',
    keywords: ['tasks', 'công việc', 'cong viec', 'task', 'assignments', 'giao việc', 'giao viec'],
    permission: PermissionEnum.PROJECT_VIEW_TASKS,
  },
  {
    id: 'timesheets',
    label: 'timesheets:title',
    path: '/timesheets',
    icon: 'clock',
    keywords: ['timesheets', 'chấm công', 'cham cong', 'time tracking', 'giờ làm', 'gio lam'],
    permission: PermissionEnum.TIMESHEET_SUBMIT_OWN,
  },
  {
    id: 'leave',
    label: 'leave:title',
    path: '/leave',
    icon: 'calendar',
    keywords: ['leave', 'nghỉ phép', 'nghi phep', 'vacation', 'absence', 'phép', 'phep'],
    permission: PermissionEnum.LEAVE_REQUEST_OWN,
  },
  {
    id: 'customers',
    label: 'customers:title',
    path: '/customers',
    icon: 'users',
    keywords: ['customers', 'khách hàng', 'khach hang', 'clients', 'client'],
    permission: PermissionEnum.CUSTOMER_VIEW_LIST,
  },
  {
    id: 'sales-orders',
    label: 'sales:orders:title',
    path: '/sales-orders',
    icon: 'shopping-cart',
    keywords: ['sales orders', 'đơn hàng', 'don hang', 'orders', 'sales'],
    permission: PermissionEnum.SALES_ORDER_VIEW_LIST,
  },
  {
    id: 'invoices',
    label: 'invoices:title',
    path: '/invoices',
    icon: 'document',
    keywords: ['invoices', 'hóa đơn', 'hoa don', 'billing', 'bills'],
    permission: PermissionEnum.INVOICE_SALES_VIEW_LIST,
  },
  {
    id: 'payments',
    label: 'payments:title',
    path: '/payments',
    icon: 'payment',
    keywords: ['payments', 'thanh toán', 'thanh toan', 'transactions', 'giao dịch', 'giao dich'],
    permission: PermissionEnum.PAYMENT_RECEIVABLE_VIEW_LIST,
  },
  {
    id: 'inventory',
    label: 'inventory:title',
    path: '/inventory',
    icon: 'server',
    keywords: ['inventory', 'kho', 'ton kho', 'stock', 'hàng hóa', 'hang hoa'],
    permission: PermissionEnum.INVENTORY_VIEW_ITEMS,
  },
  {
    id: 'reports',
    label: 'reports:title',
    path: '/reports',
    icon: 'chart',
    keywords: ['reports', 'báo cáo', 'bao cao', 'analytics', 'phân tích', 'phan tich'],
    permission: PermissionEnum.REPORT_VIEW_STANDARD,
  },
  {
    id: 'settings',
    label: 'common:settings',
    path: '/settings',
    icon: 'settings',
    keywords: ['settings', 'cài đặt', 'cai dat', 'thiết lập', 'thiet lap', 'config'],
    permission: PermissionEnum.SYSTEM_SETTING_MANAGE_GENERAL,
  },
  {
    id: 'help',
    label: 'common:help',
    path: '/help',
    icon: 'help-circle',
    keywords: [
      'help',
      'trợ giúp',
      'tro giup',
      'hỗ trợ',
      'ho tro',
      'support',
      'hướng dẫn',
      'huong dan',
    ],
    permission: PermissionEnum.DASHBOARD_VIEW_OWN, // Quyền mặc định để xem trợ giúp
  },
];

/**
 * Danh sách menu cho quản trị viên
 */
export const adminMenuItems: ModernMenuItem[] = [
  {
    id: 'admin-dashboard',
    label: 'admin:dashboard',
    path: '/admin',
    icon: 'home',
    keywords: ['admin', 'dashboard', 'trang quản trị', 'trang quan tri', 'quản trị', 'quan tri'],
    permission: PermissionEnum.DASHBOARD_VIEW_MANAGEMENT,
  },
  {
    id: 'admin-users',
    label: 'admin:users',
    path: '/admin/users',
    icon: 'user',
    keywords: ['users', 'người dùng', 'nguoi dung', 'khách hàng', 'khach hang', 'customers'],
    permission: PermissionEnum.USER_VIEW_LIST,
  },
  {
    id: 'admin-roles',
    label: 'admin:roles',
    path: '/admin/roles',
    icon: 'lock',
    keywords: ['roles', 'vai trò', 'vai tro', 'permissions', 'quyền', 'quyen'],
    permission: PermissionEnum.ROLE_VIEW_LIST,
  },
  {
    id: 'admin-permissions',
    label: 'admin:permissions',
    path: '/admin/permissions',
    icon: 'eye',
    keywords: ['permissions', 'quyền', 'quyen', 'quyền hạn', 'quyen han', 'access'],
    permission: PermissionEnum.PERMISSION_VIEW_LIST,
  },
  {
    id: 'admin-employees',
    label: 'admin:employees',
    path: '/admin/employees',
    icon: 'users',
    keywords: [
      'employees',
      'nhân viên',
      'nhan vien',
      'staff',
      'quản lý nhân viên',
      'quan ly nhan vien',
    ],
    permission: PermissionEnum.EMPLOYEE_VIEW_LIST,
  },
  {
    id: 'admin-departments',
    label: 'admin:departments',
    path: '/admin/departments',
    icon: 'building',
    keywords: ['departments', 'phòng ban', 'phong ban', 'teams', 'nhóm', 'nhom'],
    permission: PermissionEnum.DEPARTMENT_VIEW_LIST,
  },
  {
    id: 'admin-projects',
    label: 'admin:projects',
    path: '/admin/projects',
    icon: 'folder',
    keywords: [
      'projects',
      'dự án',
      'du an',
      'project management',
      'quản lý dự án',
      'quan ly du an',
    ],
    permission: PermissionEnum.PROJECT_VIEW_LIST,
  },
  {
    id: 'admin-customers',
    label: 'admin:customers',
    path: '/admin/customers',
    icon: 'users',
    keywords: ['customers', 'khách hàng', 'khach hang', 'clients', 'client management'],
    permission: PermissionEnum.CUSTOMER_VIEW_LIST,
  },
  {
    id: 'admin-sales-orders',
    label: 'admin:sales-orders',
    path: '/admin/sales-orders',
    icon: 'shopping-cart',
    keywords: ['sales orders', 'đơn hàng', 'don hang', 'orders', 'sales management'],
    permission: PermissionEnum.SALES_ORDER_VIEW_LIST,
  },
  {
    id: 'admin-purchase-orders',
    label: 'admin:purchase-orders',
    path: '/admin/purchase-orders',
    icon: 'shopping-cart',
    keywords: ['purchase orders', 'đơn mua hàng', 'don mua hang', 'purchases', 'mua hàng'],
    permission: PermissionEnum.PURCHASE_ORDER_VIEW_LIST,
  },
  {
    id: 'admin-inventory',
    label: 'admin:inventory',
    path: '/admin/inventory',
    icon: 'server',
    keywords: ['inventory', 'kho', 'ton kho', 'stock', 'hàng hóa', 'hang hoa'],
    permission: PermissionEnum.INVENTORY_VIEW_ITEMS,
  },
  {
    id: 'admin-warehouses',
    label: 'admin:warehouses',
    path: '/admin/warehouses',
    icon: 'building',
    keywords: ['warehouses', 'kho hàng', 'kho hang', 'storage', 'quản lý kho', 'quan ly kho'],
    permission: PermissionEnum.WAREHOUSE_MANAGE,
  },
  {
    id: 'admin-suppliers',
    label: 'admin:suppliers',
    path: '/admin/suppliers',
    icon: 'users',
    keywords: ['suppliers', 'nhà cung cấp', 'nha cung cap', 'vendors', 'quản lý nhà cung cấp'],
    permission: PermissionEnum.SUPPLIER_VIEW_LIST,
  },
  {
    id: 'admin-invoices',
    label: 'admin:invoices',
    path: '/admin/invoices',
    icon: 'document',
    keywords: ['invoices', 'hóa đơn', 'hoa don', 'billing', 'bills'],
    permission: PermissionEnum.INVOICE_SALES_VIEW_LIST,
  },
  {
    id: 'admin-payments',
    label: 'admin:payments',
    path: '/admin/payments',
    icon: 'payment',
    keywords: ['payments', 'thanh toán', 'thanh toan', 'transactions', 'giao dịch', 'giao dich'],
    permission: PermissionEnum.PAYMENT_RECEIVABLE_VIEW_LIST,
  },
  {
    id: 'admin-timesheets',
    label: 'admin:timesheets',
    path: '/admin/timesheets',
    icon: 'clock',
    keywords: ['timesheets', 'chấm công', 'cham cong', 'time tracking', 'quản lý chấm công'],
    permission: PermissionEnum.TIMESHEET_VIEW_ALL,
  },
  {
    id: 'admin-leave',
    label: 'admin:leave',
    path: '/admin/leave',
    icon: 'calendar',
    keywords: ['leave', 'nghỉ phép', 'nghi phep', 'vacation', 'absence management'],
    permission: PermissionEnum.LEAVE_VIEW_ALL,
  },
  {
    id: 'admin-reports',
    label: 'admin:reports',
    path: '/admin/reports',
    icon: 'chart',
    keywords: ['reports', 'báo cáo', 'bao cao', 'analytics', 'phân tích', 'phan tich'],
    permission: PermissionEnum.REPORT_VIEW_STANDARD,
  },
  {
    id: 'admin-okr',
    label: 'admin:okr',
    path: '/admin/okr',
    icon: 'star',
    keywords: ['okr', 'mục tiêu', 'muc tieu', 'objectives', 'key results'],
    permission: PermissionEnum.OKR_CREATE_COMPANY,
  },
  {
    id: 'admin-audit-logs',
    label: 'admin:audit-logs',
    path: '/admin/audit-logs',
    icon: 'list',
    keywords: ['audit logs', 'nhật ký', 'nhat ky', 'hoạt động', 'hoat dong', 'logs'],
    permission: PermissionEnum.AUDIT_LOG_VIEW,
  },
  {
    id: 'admin-settings',
    label: 'admin:settings',
    path: '/admin/settings',
    icon: 'settings',
    keywords: ['settings', 'cài đặt', 'cai dat', 'thiết lập', 'thiet lap', 'config'],
    permission: PermissionEnum.SYSTEM_SETTING_MANAGE_GENERAL,
  },
];

/**
 * Lọc menu items dựa trên danh sách quyền
 * @param menuItems Danh sách menu items cần lọc
 * @param permissions Danh sách quyền của người dùng
 * @returns Danh sách menu items mà người dùng có quyền truy cập
 */
export const getMenuItemsByPermissions = (
  menuItems: ModernMenuItem[],
  permissions: PermissionEnum[]
): ModernMenuItem[] => {
  return menuItems.filter(item => permissions.includes(item.permission));
};
